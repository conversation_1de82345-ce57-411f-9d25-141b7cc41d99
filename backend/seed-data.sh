#!/bin/bash

# GameFlex Data Seeding Wrapper Script
# This script runs the comprehensive AWS data seeding for the SAM backend

set -e

echo "🌱 GameFlex Data Seeding"
echo "========================"
echo

# Get the directory of this script
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Check if we're in the backend directory
if [ ! -f "$SCRIPT_DIR/template.yaml" ]; then
    echo "❌ Error: This script must be run from the backend directory"
    echo "   Current directory: $SCRIPT_DIR"
    echo "   Expected to find: template.yaml"
    exit 1
fi

# Check if LocalStack is running (optional check)
echo "🔍 Checking LocalStack connection..."
if ! curl -s http://localhost:4566/_localstack/health > /dev/null 2>&1; then
    echo "⚠️  Warning: LocalStack doesn't seem to be running on localhost:4566"
    echo "   Make sure LocalStack is started before running this script"
    echo "   You can start it with: localstack start"
    echo
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Aborted."
        exit 1
    fi
fi

echo "🚀 Starting data seeding process..."
echo

# Set environment variables for the seeding script
export ENVIRONMENT=${ENVIRONMENT:-development}
export PROJECT_NAME=${PROJECT_NAME:-gameflex}
export AWS_REGION=${AWS_REGION:-us-east-1}
export AWS_ENDPOINT_URL=${AWS_ENDPOINT_URL:-http://localhost:4566}

# Run the comprehensive seeding script
"$SCRIPT_DIR/scripts/seed-aws-data.sh"

echo
echo "✅ Data seeding completed!"
echo
echo "📋 Next steps:"
echo "   1. Start your SAM backend: ./start.sh"
echo "   2. Test the API endpoints at http://localhost:3000"
echo "   3. Check the seeded data in your Flutter app"
echo
echo "🔑 Test user credentials:"
echo "   📧 <EMAIL> / DevPassword123!"
echo "   👑 <EMAIL> / AdminPassword123!"
echo "   👤 <EMAIL> / JohnPassword123!"
echo "   👤 <EMAIL> / JanePassword123!"
echo "   👤 <EMAIL> / MikePassword123!"
echo
