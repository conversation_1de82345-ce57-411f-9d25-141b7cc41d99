# GameFlex Backend Data Seeding

This document explains how to seed your GameFlex SAM backend with test data for development.

## Overview

The seeding system provides comprehensive test data including:
- **Users**: 8 test users with Cognito authentication
- **User Profiles**: Complete profile information for all users
- **Channels**: 5 gaming channels (Fortnite, Valorant, <PERSON>craft, etc.)
- **Posts**: 2 sample posts with media attachments
- **Comments**: 10 comments across the posts
- **Likes**: 14 likes distributed across posts
- **Media Files**: Test images uploaded to S3

## Quick Start

1. **Ensure LocalStack is running** (if using local development):
   ```bash
   localstack start
   ```

2. **Run the seeding script**:
   ```bash
   cd backend
   ./seed-data.sh
   ```

## What Gets Seeded

### Users
- **<EMAIL>** - Developer account (password: DevPassword123!)
- **<EMAIL>** - Admin account (password: AdminPassword123!)
- **<EMAIL>** - Test user (password: John<PERSON>assword123!)
- **<EMAIL>** - Test user (password: <PERSON><PERSON><PERSON>word123!)
- **<EMAIL>** - Test user (password: <PERSON><PERSON><PERSON>word123!)
- **<EMAIL>** - Test user (password: AlicePassword123!)
- **<EMAIL>** - Test user (password: BobPassword123!)
- **<EMAIL>** - Test user (password: CharliePassword123!)

### Gaming Channels
- **Fortnite** - Tips, tricks, and epic moments
- **Valorant** - Tactical FPS discussions and strategies
- **Minecraft** - Creative builds and survival tips
- **League of Legends** - MOBA strategies and esports news
- **Indie Games** - Discover amazing indie games

### Sample Posts
- **John's Call of Duty Post** - Epic gaming moment with screenshot
- **Jane's Diablo Post** - Boss defeat celebration with screenshot

### Media Files
- `cod_screenshot.jpg` - Call of Duty gameplay screenshot
- `diablo_screenshot.webp` - Diablo boss fight screenshot

## Smart Seeding Features

The seeding script is designed to be **idempotent** and **safe**:

- ✅ **Skip Existing Data**: Won't duplicate data if run multiple times
- ✅ **Cognito Integration**: Links database users with actual Cognito accounts
- ✅ **S3 Upload**: Automatically uploads test images to your media bucket
- ✅ **Error Handling**: Gracefully handles missing resources
- ✅ **Progress Reporting**: Clear status messages throughout the process

## Environment Configuration

The script uses these environment variables (with defaults):

```bash
ENVIRONMENT=development          # Environment name
PROJECT_NAME=gameflex           # Project name for resource naming
AWS_REGION=us-east-1           # AWS region
AWS_ENDPOINT_URL=http://localhost:4566  # LocalStack endpoint
```

You can override these by setting them before running:

```bash
export ENVIRONMENT=staging
export AWS_ENDPOINT_URL=https://your-aws-endpoint.com
./seed-data.sh
```

## Manual Seeding

If you need to run specific parts of the seeding process:

```bash
# Run the comprehensive seeding script directly
./scripts/seed-aws-data.sh

# Or run with custom environment
ENVIRONMENT=staging ./scripts/seed-aws-data.sh
```

## Troubleshooting

### LocalStack Connection Issues
```
❌ Error: Failed to connect to AWS/LocalStack
```
**Solution**: Ensure LocalStack is running on port 4566:
```bash
localstack start
curl http://localhost:4566/_localstack/health
```

### Missing Media Files
```
❌ Error: Media file not found: /path/to/cod_screenshot.jpg
```
**Solution**: Ensure media files exist in `backend/assets/media/`:
```bash
ls -la backend/assets/media/
```

### Cognito User Pool Not Found
```
⚠️ Warning: User Pool not found, using placeholder Cognito IDs
```
**Solution**: Run the AWS services initialization first:
```bash
./scripts/init-aws-services.sh
```

### DynamoDB Table Not Found
```
❌ Error: Failed to add user to gameflex-development-Users
```
**Solution**: Deploy your SAM application first:
```bash
sam build
sam deploy
```

## Integration with SAM Backend

The seeding script is designed to work with your SAM backend:

1. **Table Names**: Uses SAM naming convention (`${ProjectName}-${Environment}-${TableName}`)
2. **S3 Buckets**: Uses SAM bucket names (`${ProjectName}-media-${Environment}`)
3. **Cognito Integration**: Links with your SAM-created User Pool
4. **Environment Variables**: Matches your SAM template configuration

## Testing the Seeded Data

After seeding, you can test the data:

1. **Start your backend**:
   ```bash
   ./start.sh
   ```

2. **Test API endpoints**:
   ```bash
   # Health check
   curl http://localhost:3000/health
   
   # Get posts
   curl http://localhost:3000/posts
   
   # Get users
   curl http://localhost:3000/users
   ```

3. **Login with test users** in your Flutter app using the provided credentials.

## Cleaning Up

To clear all seeded data and start fresh:

1. **Clear DynamoDB tables** (if using LocalStack):
   ```bash
   # This will clear all data - use with caution!
   aws --endpoint-url=http://localhost:4566 dynamodb scan --table-name gameflex-development-Posts --query 'Items[].id.S' --output text | xargs -I {} aws --endpoint-url=http://localhost:4566 dynamodb delete-item --table-name gameflex-development-Posts --key '{"id":{"S":"{}"}}'
   ```

2. **Clear S3 bucket**:
   ```bash
   aws --endpoint-url=http://localhost:4566 s3 rm s3://gameflex-media-development --recursive
   ```

3. **Re-run seeding**:
   ```bash
   ./seed-data.sh
   ```
