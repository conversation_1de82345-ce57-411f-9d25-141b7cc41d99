#!/bin/bash

set -e

echo "Testing simplified seeding..."

# Colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Simple put_item function
put_item() {
    local table_name=$1
    local item_json=$2
    local item_description=$3

    print_status "Adding $item_description to $table_name..."
    
    local temp_file=$(mktemp)
    echo "$item_json" > "$temp_file"
    
    if timeout 30 aws dynamodb put-item \
        --table-name "$table_name" \
        --item file://"$temp_file" > /dev/null 2>&1; then
        print_status "✅ Successfully added $item_description"
        rm -f "$temp_file"
        return 0
    else
        print_error "❌ Failed to add $item_description"
        rm -f "$temp_file"
        return 1
    fi
}

# Test seeding one user
print_status "Testing user seeding..."

put_item "gameflex-development-Users" '{
    "id": {"S": "test-seed-user"},
    "cognito_user_id": {"S": "test-cognito-id"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "testseed"},
    "display_name": {"S": "Test Seed User"},
    "bio": {"S": "This is a test seed user"},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "test seed user"

print_status "Test seeding completed!"
