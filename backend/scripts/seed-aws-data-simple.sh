#!/bin/bash

# GameFlex AWS Data Seeding Script - Simplified Version
# This script seeds AWS services with test data for development

echo "[SEED] Starting GameFlex AWS data seeding..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SEED]${NC} $1"
}

# Environment variables
ENVIRONMENT=${ENVIRONMENT:-development}
PROJECT_NAME=${PROJECT_NAME:-gameflex}
AWS_REGION=${AWS_REGION:-us-west-2}

# Table names
USERS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Users"
USER_PROFILES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles"
POSTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Posts"
MEDIA_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Media"
COMMENTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Comments"
LIKES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Likes"
CHANNELS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Channels"
FOLLOWS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Follows"
MEDIA_BUCKET="${PROJECT_NAME}-media-${ENVIRONMENT}"

# Simple put_item function
put_item() {
    local table_name=$1
    local item_json=$2
    local item_description=$3

    print_status "Adding $item_description to $table_name..."
    
    local temp_file=$(mktemp)
    echo "$item_json" > "$temp_file"
    
    if timeout 30 aws dynamodb put-item \
        --table-name "$table_name" \
        --item file://"$temp_file" > /dev/null 2>&1; then
        print_status "✅ Successfully added $item_description"
    else
        print_error "❌ Failed to add $item_description"
    fi
    
    rm -f "$temp_file"
}

# Test AWS connection
print_status "Testing AWS connection..."
if aws sts get-caller-identity > /dev/null 2>&1; then
    print_status "AWS CLI connection successful"
else
    print_error "Failed to connect to AWS"
    exit 1
fi

# Seed Users
print_header "Seeding Users table..."

put_item "$USERS_TABLE" '{
    "id": {"S": "550e8400-e29b-41d4-a716-************"},
    "cognito_user_id": {"S": "dev-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "developer"},
    "display_name": {"S": "GameFlex Developer"},
    "bio": {"S": "Development account for testing GameFlex features"},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "developer user"

put_item "$USERS_TABLE" '{
    "id": {"S": "550e8400-e29b-41d4-a716-************"},
    "cognito_user_id": {"S": "admin-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "admin"},
    "display_name": {"S": "GameFlex Admin"},
    "bio": {"S": "Administrator account with full access"},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "admin user"

put_item "$USERS_TABLE" '{
    "id": {"S": "********-0000-0000-0000-********0003"},
    "cognito_user_id": {"S": "john-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "johndoe"},
    "display_name": {"S": "John Doe"},
    "bio": {"S": "Gaming enthusiast and content creator."},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "john doe user"

put_item "$USERS_TABLE" '{
    "id": {"S": "********-0000-0000-0000-************"},
    "cognito_user_id": {"S": "jane-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "janesmith"},
    "display_name": {"S": "Jane Smith"},
    "bio": {"S": "Professional gamer and streamer."},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "jane smith user"

put_item "$USERS_TABLE" '{
    "id": {"S": "********-0000-0000-0000-********0005"},
    "cognito_user_id": {"S": "mike-cognito-placeholder"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "mikewilson"},
    "display_name": {"S": "Mike Wilson"},
    "bio": {"S": "Casual gamer who loves sharing gaming moments."},
    "is_active": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "mike wilson user"

print_status "Users seeded successfully!"
echo

# Seed UserProfiles
print_header "Seeding UserProfiles table..."

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "first_name": {"S": "Dev"},
    "last_name": {"S": "User"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/New_York"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "dev profile"

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "first_name": {"S": "Admin"},
    "last_name": {"S": "User"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/Los_Angeles"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "admin profile"

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "********-0000-0000-0000-********0003"},
    "first_name": {"S": "John"},
    "last_name": {"S": "Doe"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/New_York"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "john profile"

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "********-0000-0000-0000-************"},
    "first_name": {"S": "Jane"},
    "last_name": {"S": "Smith"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/Los_Angeles"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "jane profile"

put_item "$USER_PROFILES_TABLE" '{
    "user_id": {"S": "********-0000-0000-0000-********0005"},
    "first_name": {"S": "Mike"},
    "last_name": {"S": "Wilson"},
    "country": {"S": "United States"},
    "timezone": {"S": "America/Chicago"},
    "language": {"S": "en"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "mike profile"

print_status "User profiles seeded successfully!"
echo

# Seed Channels
print_header "Seeding Channels table..."

put_item "$CHANNELS_TABLE" '{
    "id": {"S": "660e8400-e29b-41d4-a716-************"},
    "name": {"S": "Fortnite"},
    "description": {"S": "Everything about Fortnite - tips, tricks, and epic moments"},
    "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "is_public": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "member_count": {"N": "150"},
    "post_count": {"N": "45"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "fortnite channel"

put_item "$CHANNELS_TABLE" '{
    "id": {"S": "660e8400-e29b-41d4-a716-************"},
    "name": {"S": "Valorant"},
    "description": {"S": "Tactical FPS discussions, strategies, and highlights"},
    "owner_id": {"S": "550e8400-e29b-41d4-a716-************"},
    "is_public": {"BOOL": true},
    "is_verified": {"BOOL": true},
    "member_count": {"N": "200"},
    "post_count": {"N": "67"},
    "created_at": {"S": "2024-01-01T00:00:00Z"},
    "updated_at": {"S": "2024-01-01T00:00:00Z"}
}' "valorant channel"

print_status "Channels seeded successfully!"
echo

# Seed Media
print_header "Seeding Media table..."

put_item "$MEDIA_TABLE" '{
    "id": {"S": "30000000-0000-0000-0000-********0001"},
    "type": {"S": "image"},
    "location": {"S": "user"},
    "name": {"S": "cod_screenshot"},
    "extension": {"S": "jpg"},
    "owner_id": {"S": "********-0000-0000-0000-********0003"},
    "bucket_location": {"S": "s3"},
    "bucket_name": {"S": "'$MEDIA_BUCKET'"},
    "bucket_permission": {"S": "public"},
    "s3_url": {"S": "https://'$MEDIA_BUCKET'.s3.'$AWS_REGION'.amazonaws.com/user/********-0000-0000-0000-********0003/cod_screenshot.jpg"},
    "width": {"N": "1920"},
    "height": {"N": "1080"},
    "size_bytes": {"N": "245760"},
    "mime_type": {"S": "image/jpeg"},
    "created_at": {"S": "2024-12-28T14:30:00Z"},
    "updated_at": {"S": "2024-12-28T14:30:00Z"}
}' "cod screenshot media"

put_item "$MEDIA_TABLE" '{
    "id": {"S": "30000000-0000-0000-0000-********0002"},
    "type": {"S": "image"},
    "location": {"S": "user"},
    "name": {"S": "diablo_screenshot"},
    "extension": {"S": "webp"},
    "owner_id": {"S": "********-0000-0000-0000-************"},
    "bucket_location": {"S": "s3"},
    "bucket_name": {"S": "'$MEDIA_BUCKET'"},
    "bucket_permission": {"S": "public"},
    "s3_url": {"S": "https://'$MEDIA_BUCKET'.s3.'$AWS_REGION'.amazonaws.com/user/********-0000-0000-0000-************/diablo_screenshot.webp"},
    "width": {"N": "1920"},
    "height": {"N": "1080"},
    "size_bytes": {"N": "189440"},
    "mime_type": {"S": "image/webp"},
    "created_at": {"S": "2024-12-27T16:45:00Z"},
    "updated_at": {"S": "2024-12-27T16:45:00Z"}
}' "diablo screenshot media"

print_status "Media seeded successfully!"
echo

print_status "🎉 Basic seeding completed successfully!"
print_status "Seeded 5 users with profiles, 2 channels, and 2 media items"
