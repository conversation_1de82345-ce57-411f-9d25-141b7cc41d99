#!/bin/bash

# GameFlex AWS Data Seeding Script for SAM Backend
# This script seeds AWS services with test data for development
# Adapted from aws-backend seeding scripts for SAM backend

set -e

echo "[SEED] Starting GameFlex AWS data seeding..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SEED]${NC} $1"
}

# Environment variables
ENVIRONMENT=${ENVIRONMENT:-development}
PROJECT_NAME=${PROJECT_NAME:-gameflex}
AWS_REGION=${AWS_REGION:-us-east-1}
AWS_ENDPOINT_URL=${AWS_ENDPOINT_URL:-http://localhost:4566}

# Table names (SAM format)
USERS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Users"
USER_PROFILES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-UserProfiles"
POSTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Posts"
MEDIA_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Media"
COMMENTS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Comments"
LIKES_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Likes"
CHANNELS_TABLE="${PROJECT_NAME}-${ENVIRONMENT}-Channels"

# S3 bucket names (SAM format)
MEDIA_BUCKET="${PROJECT_NAME}-media-${ENVIRONMENT}"

# Function to check if item exists in DynamoDB
item_exists() {
    local table_name=$1
    local key_condition=$2
    
    local result=$(aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb get-item \
        --table-name "$table_name" \
        --key "$key_condition" \
        --query 'Item' \
        --output text 2>/dev/null || echo "None")
    
    if [ "$result" != "None" ] && [ "$result" != "" ]; then
        return 0  # Item exists
    else
        return 1  # Item doesn't exist
    fi
}

# Function to put item in DynamoDB
put_item() {
    local table_name=$1
    local item_json=$2
    local item_description=$3

    echo "$item_json" | aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb put-item \
        --table-name "$table_name" \
        --item file:///dev/stdin > /dev/null 2>&1

    if [ $? -eq 0 ]; then
        print_status "Added $item_description to $table_name"
    else
        print_error "Failed to add $item_description to $table_name"
        return 1
    fi
}

# Function to get Cognito user ID by email
get_cognito_user_id() {
    local email=$1
    local user_pool_id=$2

    local cognito_user_id=$(aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp list-users \
        --user-pool-id "$user_pool_id" \
        --filter "email = \"$email\"" \
        --query 'Users[0].Username' \
        --output text 2>/dev/null || echo "None")

    if [ "$cognito_user_id" != "None" ] && [ "$cognito_user_id" != "" ]; then
        echo "$cognito_user_id"
    else
        echo "cognito-user-not-found-$email"
    fi
}

# Function to get User Pool ID
get_user_pool_id() {
    local user_pool_id=$(aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp list-user-pools \
        --max-results 50 \
        --query "UserPools[?contains(Name, '${PROJECT_NAME}')].Id" \
        --output text 2>/dev/null | head -n1)

    if [ -z "$user_pool_id" ] || [ "$user_pool_id" = "None" ]; then
        print_warning "User Pool not found, using placeholder Cognito IDs"
        echo ""
    else
        print_status "Found User Pool: $user_pool_id"
        echo "$user_pool_id"
    fi
}

# Test AWS connection
test_aws_connection() {
    print_status "Testing AWS connection..."
    if aws --endpoint-url=$AWS_ENDPOINT_URL sts get-caller-identity > /dev/null 2>&1; then
        print_status "AWS CLI connection successful"
        return 0
    else
        print_error "Failed to connect to AWS/LocalStack"
        return 1
    fi
}

# Create additional Cognito users for seeding
create_additional_cognito_users() {
    print_header "Creating additional Cognito users for seeding..."
    
    local user_pool_id=$(get_user_pool_id)
    if [ -z "$user_pool_id" ]; then
        print_error "Cannot create users without User Pool"
        return 1
    fi

    # Array of users to create
    local users=(
        "<EMAIL>:johndoe:John:Doe:JohnPassword123!"
        "<EMAIL>:janesmith:Jane:Smith:JanePassword123!"
        "<EMAIL>:mikewilson:Mike:Wilson:MikePassword123!"
        "<EMAIL>:alice_gamer:Alice:Cooper:AlicePassword123!"
        "<EMAIL>:bob_streamer:Bob:Wilson:BobPassword123!"
        "<EMAIL>:charlie_dev:Charlie:Brown:CharliePassword123!"
    )

    for user_data in "${users[@]}"; do
        IFS=':' read -r email username first_name last_name password <<< "$user_data"
        
        # Check if user already exists
        local existing_user=$(aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp list-users \
            --user-pool-id "$user_pool_id" \
            --filter "email = \"$email\"" \
            --query 'Users[0].Username' \
            --output text 2>/dev/null || echo "None")
        
        if [ "$existing_user" != "None" ] && [ "$existing_user" != "" ]; then
            print_status "User $email already exists, skipping"
            continue
        fi

        # Create user
        aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp admin-create-user \
            --user-pool-id "$user_pool_id" \
            --username "$email" \
            --user-attributes Name=email,Value="$email" Name=email_verified,Value=true Name=given_name,Value="$first_name" Name=family_name,Value="$last_name" \
            --temporary-password "TempPassword123!" \
            --message-action SUPPRESS > /dev/null 2>&1 || true

        # Set permanent password
        aws --endpoint-url=$AWS_ENDPOINT_URL cognito-idp admin-set-user-password \
            --user-pool-id "$user_pool_id" \
            --username "$email" \
            --password "$password" \
            --permanent > /dev/null 2>&1 || true

        print_status "Created user: $email"
    done
}

# Seed Users table
seed_users() {
    print_header "Seeding Users table..."

    local user_pool_id=$(get_user_pool_id)

    # Get actual Cognito user IDs
    local dev_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local admin_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local john_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local jane_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local mike_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local alice_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local bob_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local charlie_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")

    # Array of users to seed
    local users=(
        '550e8400-e29b-41d4-a716-************:'"$dev_cognito_id"':<EMAIL>:developer:GameFlex Developer:Development account for testing GameFlex features'
        '550e8400-e29b-41d4-a716-************:'"$admin_cognito_id"':<EMAIL>:admin:GameFlex Admin:Administrator account with full access'
        '********-0000-0000-0000-********0003:'"$john_cognito_id"':<EMAIL>:johndoe:John Doe:Gaming enthusiast and content creator.'
        '********-0000-0000-0000-********0004:'"$jane_cognito_id"':<EMAIL>:janesmith:Jane Smith:Professional gamer and streamer.'
        '********-0000-0000-0000-********0005:'"$mike_cognito_id"':<EMAIL>:mikewilson:Mike Wilson:Casual gamer who loves sharing gaming moments.'
        '550e8400-e29b-41d4-a716-************:'"$alice_cognito_id"':<EMAIL>:alice_gamer:Alice Cooper:Passionate gamer and content creator'
        '550e8400-e29b-41d4-a716-************:'"$bob_cognito_id"':<EMAIL>:bob_streamer:Bob Wilson:Professional esports player and streamer'
        '550e8400-e29b-41d4-a716-************:'"$charlie_cognito_id"':<EMAIL>:charlie_dev:Charlie Brown:Game developer and indie creator'
    )

    for user_data in "${users[@]}"; do
        IFS=':' read -r user_id cognito_id email username display_name bio <<< "$user_data"

        # Check if user already exists
        if item_exists "$USERS_TABLE" '{"id":{"S":"'$user_id'"}}'; then
            print_status "User $email already exists, skipping"
            continue
        fi

        put_item "$USERS_TABLE" '{
            "id": {"S": "'$user_id'"},
            "cognito_user_id": {"S": "'$cognito_id'"},
            "email": {"S": "'$email'"},
            "username": {"S": "'$username'"},
            "display_name": {"S": "'$display_name'"},
            "bio": {"S": "'$bio'"},
            "is_active": {"BOOL": true},
            "is_verified": {"BOOL": true},
            "created_at": {"S": "2024-01-01T00:00:00Z"},
            "updated_at": {"S": "2024-01-01T00:00:00Z"}
        }' "user $email"
    done

    print_status "Users seeded with Cognito IDs:"
    echo "  - <EMAIL>: $dev_cognito_id"
    echo "  - <EMAIL>: $admin_cognito_id"
    echo "  - <EMAIL>: $john_cognito_id"
    echo "  - <EMAIL>: $jane_cognito_id"
    echo "  - <EMAIL>: $mike_cognito_id"
    echo "  - <EMAIL>: $alice_cognito_id"
    echo "  - <EMAIL>: $bob_cognito_id"
    echo "  - <EMAIL>: $charlie_cognito_id"
}

# Seed UserProfiles table
seed_user_profiles() {
    print_header "Seeding UserProfiles table..."

    # Array of user profiles
    local profiles=(
        '550e8400-e29b-41d4-a716-************:Dev:User:United States:America/New_York:en'
        '550e8400-e29b-41d4-a716-************:Admin:User:United States:America/Los_Angeles:en'
        '********-0000-0000-0000-********0003:John:Doe:United States:America/New_York:en'
        '********-0000-0000-0000-********0004:Jane:Smith:United States:America/Los_Angeles:en'
        '********-0000-0000-0000-********0005:Mike:Wilson:United States:America/Chicago:en'
        '550e8400-e29b-41d4-a716-************:Alice:Cooper:Canada:America/Toronto:en'
        '550e8400-e29b-41d4-a716-************:Bob:Wilson:United Kingdom:Europe/London:en'
        '550e8400-e29b-41d4-a716-************:Charlie:Brown:Australia:Australia/Sydney:en'
    )

    for profile_data in "${profiles[@]}"; do
        IFS=':' read -r user_id first_name last_name country timezone language <<< "$profile_data"

        # Check if profile already exists
        if item_exists "$USER_PROFILES_TABLE" '{"user_id":{"S":"'$user_id'"}}'; then
            print_status "Profile for user $user_id already exists, skipping"
            continue
        fi

        put_item "$USER_PROFILES_TABLE" '{
            "user_id": {"S": "'$user_id'"},
            "first_name": {"S": "'$first_name'"},
            "last_name": {"S": "'$last_name'"},
            "country": {"S": "'$country'"},
            "timezone": {"S": "'$timezone'"},
            "language": {"S": "'$language'"},
            "created_at": {"S": "2024-01-01T00:00:00Z"},
            "updated_at": {"S": "2024-01-01T00:00:00Z"}
        }' "profile for user $user_id"
    done
}

# Seed Channels table
seed_channels() {
    print_header "Seeding Channels table..."

    # Array of channels
    local channels=(
        '660e8400-e29b-41d4-a716-************:Fortnite:Everything about Fortnite - tips, tricks, and epic moments:550e8400-e29b-41d4-a716-************:true:true:150:45'
        '660e8400-e29b-41d4-a716-************:Valorant:Tactical FPS discussions, strategies, and highlights:550e8400-e29b-41d4-a716-************:true:true:200:67'
        '660e8400-e29b-41d4-a716-************:Minecraft:Creative builds, survival tips, and community projects:550e8400-e29b-41d4-a716-************:true:false:300:89'
        '660e8400-e29b-41d4-a716-************:League of Legends:MOBA strategies, champion guides, and esports news:550e8400-e29b-41d4-a716-************:true:true:500:123'
        '660e8400-e29b-41d4-a716-************:Indie Games:Discover and discuss amazing indie games:550e8400-e29b-41d4-a716-************:true:false:75:34'
    )

    for channel_data in "${channels[@]}"; do
        IFS=':' read -r channel_id name description owner_id is_public is_verified member_count post_count <<< "$channel_data"

        # Check if channel already exists
        if item_exists "$CHANNELS_TABLE" '{"id":{"S":"'$channel_id'"}}'; then
            print_status "Channel $name already exists, skipping"
            continue
        fi

        put_item "$CHANNELS_TABLE" '{
            "id": {"S": "'$channel_id'"},
            "name": {"S": "'$name'"},
            "description": {"S": "'$description'"},
            "owner_id": {"S": "'$owner_id'"},
            "is_public": {"BOOL": '$is_public'},
            "is_verified": {"BOOL": '$is_verified'},
            "member_count": {"N": "'$member_count'"},
            "post_count": {"N": "'$post_count'"},
            "created_at": {"S": "2024-01-01T00:00:00Z"},
            "updated_at": {"S": "2024-01-01T00:00:00Z"}
        }' "channel $name"
    done
}

# Seed Media table
seed_media() {
    print_header "Seeding Media table..."

    # Array of media items
    local media_items=(
        '30000000-0000-0000-0000-********0001:image:user:cod_screenshot:jpg:********-0000-0000-0000-********0003:s3:'$MEDIA_BUCKET':public:http://localhost:4566/'$MEDIA_BUCKET'/user/********-0000-0000-0000-********0003/cod_screenshot.jpg:1920:1080:245760:image/jpeg:2024-12-28T14:30:00Z'
        '30000000-0000-0000-0000-********0002:image:user:diablo_screenshot:webp:********-0000-0000-0000-********0004:s3:'$MEDIA_BUCKET':public:http://localhost:4566/'$MEDIA_BUCKET'/user/********-0000-0000-0000-********0004/diablo_screenshot.webp:1920:1080:189440:image/webp:2024-12-27T16:45:00Z'
    )

    for media_data in "${media_items[@]}"; do
        IFS=':' read -r media_id type location name extension owner_id bucket_location bucket_name bucket_permission s3_url width height size_bytes mime_type created_at <<< "$media_data"

        # Check if media already exists
        if item_exists "$MEDIA_TABLE" '{"id":{"S":"'$media_id'"}}'; then
            print_status "Media $name already exists, skipping"
            continue
        fi

        put_item "$MEDIA_TABLE" '{
            "id": {"S": "'$media_id'"},
            "type": {"S": "'$type'"},
            "location": {"S": "'$location'"},
            "name": {"S": "'$name'"},
            "extension": {"S": "'$extension'"},
            "owner_id": {"S": "'$owner_id'"},
            "bucket_location": {"S": "'$bucket_location'"},
            "bucket_name": {"S": "'$bucket_name'"},
            "bucket_permission": {"S": "'$bucket_permission'"},
            "s3_url": {"S": "'$s3_url'"},
            "width": {"N": "'$width'"},
            "height": {"N": "'$height'"},
            "size_bytes": {"N": "'$size_bytes'"},
            "mime_type": {"S": "'$mime_type'"},
            "created_at": {"S": "'$created_at'"},
            "updated_at": {"S": "'$created_at'"}
        }' "media $name"
    done
}

# Clear Posts table
clear_posts() {
    print_status "Clearing Posts table..."

    # Get all post IDs
    local post_ids=$(aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb scan \
        --table-name "$POSTS_TABLE" \
        --query 'Items[].id.S' \
        --output text 2>/dev/null || echo "")

    if [ -n "$post_ids" ]; then
        for post_id in $post_ids; do
            print_status "Deleting post: $post_id"
            aws --endpoint-url=$AWS_ENDPOINT_URL dynamodb delete-item \
                --table-name "$POSTS_TABLE" \
                --key '{"id":{"S":"'$post_id'"}}' > /dev/null 2>&1 || true
        done
        print_status "All posts cleared"
    else
        print_status "Posts table is already empty"
    fi
}

# Seed Posts table
seed_posts() {
    print_header "Seeding Posts table..."

    # Clear existing posts first
    clear_posts

    local user_pool_id=$(get_user_pool_id)

    # Get Cognito user IDs for proper mapping
    local john_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local jane_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")

    # Array of posts
    local posts=(
        '20000000-0000-0000-0000-********0001:********-0000-0000-0000-********0003:'"$john_cognito_id"':Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥:cod_screenshot.jpg:image:'$MEDIA_BUCKET':user/********-0000-0000-0000-********0003/cod_screenshot.jpg:12:4:2024-12-28T14:30:00Z'
        '20000000-0000-0000-0000-********0002:********-0000-0000-0000-********0004:'"$jane_cognito_id"':Finally defeated this boss in Diablo! The loot was totally worth the grind 💀⚔️:diablo_screenshot.webp:image:'$MEDIA_BUCKET':user/********-0000-0000-0000-********0004/diablo_screenshot.webp:18:6:2024-12-27T16:45:00Z'
    )

    for post_data in "${posts[@]}"; do
        IFS=':' read -r post_id author_id cognito_user_id content media_id media_type s3_bucket s3_key like_count comment_count created_at <<< "$post_data"

        put_item "$POSTS_TABLE" '{
            "id": {"S": "'$post_id'"},
            "author_id": {"S": "'$author_id'"},
            "cognito_user_id": {"S": "'$cognito_user_id'"},
            "content": {"S": "'$content'"},
            "media_id": {"S": "'$media_id'"},
            "media_type": {"S": "'$media_type'"},
            "s3_bucket": {"S": "'$s3_bucket'"},
            "s3_key": {"S": "'$s3_key'"},
            "like_count": {"N": "'$like_count'"},
            "comment_count": {"N": "'$comment_count'"},
            "is_active": {"BOOL": true},
            "created_at": {"S": "'$created_at'"},
            "updated_at": {"S": "'$created_at'"}
        }' "post $post_id"
    done

    print_status "Posts seeded with Cognito IDs:"
    echo "  - <EMAIL>: $john_cognito_id"
    echo "  - <EMAIL>: $jane_cognito_id"
}

# Seed Comments table
seed_comments() {
    print_header "Seeding Comments table..."

    # Array of comments
    local comments=(
        '40000000-0000-0000-0000-********0001:20000000-0000-0000-0000-********0001:********-0000-0000-0000-********0004:Nice clutch! What loadout were you using?:3:2024-12-28T15:15:00Z'
        '40000000-0000-0000-0000-********0002:20000000-0000-0000-0000-********0001:********-0000-0000-0000-********0005:That was insane! 🔥:1:2024-12-28T16:20:00Z'
        '40000000-0000-0000-0000-********0003:20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:Great gameplay! Keep it up 👍:2:2024-12-28T17:30:00Z'
        '40000000-0000-0000-0000-********0004:20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:Which map is this? Looks intense!:0:2024-12-28T18:45:00Z'
        '40000000-0000-0000-0000-********0005:20000000-0000-0000-0000-********0002:********-0000-0000-0000-********0003:Congrats! What difficulty level?:2:2024-12-27T17:30:00Z'
        '40000000-0000-0000-0000-********0006:20000000-0000-0000-0000-********0002:********-0000-0000-0000-********0005:The loot looks amazing! 💎:4:2024-12-27T18:15:00Z'
        '40000000-0000-0000-0000-********0007:20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:Epic boss fight! How long did it take?:1:2024-12-27T19:00:00Z'
        '40000000-0000-0000-0000-********0008:20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:I need to try this boss next! Any tips?:3:2024-12-27T20:30:00Z'
        '40000000-0000-0000-0000-********0009:20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:Diablo never gets old! 🔥⚔️:2:2024-12-27T21:15:00Z'
        '40000000-0000-0000-0000-********0010:20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:What class are you playing?:0:2024-12-27T22:00:00Z'
    )

    for comment_data in "${comments[@]}"; do
        IFS=':' read -r comment_id post_id user_id content like_count created_at <<< "$comment_data"

        # Check if comment already exists
        if item_exists "$COMMENTS_TABLE" '{"id":{"S":"'$comment_id'"}}'; then
            print_status "Comment $comment_id already exists, skipping"
            continue
        fi

        put_item "$COMMENTS_TABLE" '{
            "id": {"S": "'$comment_id'"},
            "post_id": {"S": "'$post_id'"},
            "user_id": {"S": "'$user_id'"},
            "content": {"S": "'$content'"},
            "like_count": {"N": "'$like_count'"},
            "is_active": {"BOOL": true},
            "created_at": {"S": "'$created_at'"},
            "updated_at": {"S": "'$created_at'"}
        }' "comment $comment_id"
    done
}

# Seed Likes table
seed_likes() {
    print_header "Seeding Likes table..."

    # Array of likes for posts
    local likes=(
        '20000000-0000-0000-0000-********0001:********-0000-0000-0000-********0004:2024-12-28T14:35:00Z'
        '20000000-0000-0000-0000-********0001:********-0000-0000-0000-********0005:2024-12-28T14:40:00Z'
        '20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:2024-12-28T14:45:00Z'
        '20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:2024-12-28T15:00:00Z'
        '20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:2024-12-28T15:30:00Z'
        '20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:2024-12-28T16:00:00Z'
        '20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:2024-12-28T16:30:00Z'
        '20000000-0000-0000-0000-********0002:********-0000-0000-0000-********0003:2024-12-27T16:50:00Z'
        '20000000-0000-0000-0000-********0002:********-0000-0000-0000-********0005:2024-12-27T17:00:00Z'
        '20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:2024-12-27T17:15:00Z'
        '20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:2024-12-27T17:30:00Z'
        '20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:2024-12-27T18:00:00Z'
        '20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:2024-12-27T18:30:00Z'
        '20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:2024-12-27T19:00:00Z'
    )

    for like_data in "${likes[@]}"; do
        IFS=':' read -r post_id user_id created_at <<< "$like_data"

        # Check if like already exists
        if item_exists "$LIKES_TABLE" '{"post_id":{"S":"'$post_id'"},"user_id":{"S":"'$user_id'"}}'; then
            print_status "Like for post $post_id by user $user_id already exists, skipping"
            continue
        fi

        put_item "$LIKES_TABLE" '{
            "post_id": {"S": "'$post_id'"},
            "user_id": {"S": "'$user_id'"},
            "created_at": {"S": "'$created_at'"}
        }' "like for post $post_id"
    done
}

# Upload media files to S3
upload_media_files() {
    print_header "Uploading media files to S3..."

    # Check if media bucket exists, create if it doesn't
    print_status "Checking if S3 bucket exists: $MEDIA_BUCKET"
    aws --endpoint-url=$AWS_ENDPOINT_URL s3 ls "s3://$MEDIA_BUCKET" > /dev/null 2>&1
    if [ $? -ne 0 ]; then
        print_status "S3 bucket $MEDIA_BUCKET does not exist, creating it..."
        aws --endpoint-url=$AWS_ENDPOINT_URL s3 mb "s3://$MEDIA_BUCKET" > /dev/null 2>&1

        # Set bucket policy to allow public read access
        aws --endpoint-url=$AWS_ENDPOINT_URL s3api put-bucket-policy \
            --bucket "$MEDIA_BUCKET" \
            --policy '{
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Sid": "PublicReadGetObject",
                        "Effect": "Allow",
                        "Principal": "*",
                        "Action": "s3:GetObject",
                        "Resource": "arn:aws:s3:::'$MEDIA_BUCKET'/*"
                    }
                ]
            }' > /dev/null 2>&1

        print_status "Created S3 bucket $MEDIA_BUCKET with public read policy"
    else
        print_status "S3 bucket $MEDIA_BUCKET already exists"
    fi

    # Function to upload file to S3
    upload_media_file() {
        local file_path=$1
        local s3_key=$2
        local content_type=$3

        if [ ! -f "$file_path" ]; then
            print_error "Media file not found: $file_path"
            return 1
        fi

        # Check if file already exists in S3
        aws --endpoint-url=$AWS_ENDPOINT_URL s3 ls "s3://$MEDIA_BUCKET/$s3_key" > /dev/null 2>&1
        if [ $? -eq 0 ]; then
            print_status "File $s3_key already exists in S3, skipping"
            return 0
        fi

        print_status "Uploading $file_path to s3://$MEDIA_BUCKET/$s3_key"

        aws --endpoint-url=$AWS_ENDPOINT_URL s3 cp "$file_path" "s3://$MEDIA_BUCKET/$s3_key" \
            --content-type "$content_type" \
            --metadata-directive REPLACE > /dev/null 2>&1

        if [ $? -eq 0 ]; then
            print_status "Successfully uploaded $s3_key"

            # Verify the upload
            aws --endpoint-url=$AWS_ENDPOINT_URL s3 ls "s3://$MEDIA_BUCKET/$s3_key" > /dev/null 2>&1
            if [ $? -eq 0 ]; then
                print_status "Verified $s3_key exists in S3"
            else
                print_warning "Could not verify $s3_key in S3"
            fi
        else
            print_error "Failed to upload $s3_key"
            return 1
        fi
    }

    # Upload media files for seeded posts
    print_status "Uploading media files for seeded posts..."

    # Get the script directory to find assets
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    ASSETS_DIR="$(dirname "$SCRIPT_DIR")/assets/media"

    # Upload COD screenshot for John Doe's post
    upload_media_file \
        "$ASSETS_DIR/cod_screenshot.jpg" \
        "user/********-0000-0000-0000-********0003/cod_screenshot.jpg" \
        "image/jpeg"

    # Upload Diablo screenshot for Jane Smith's post
    upload_media_file \
        "$ASSETS_DIR/diablo_screenshot.webp" \
        "user/********-0000-0000-0000-********0004/diablo_screenshot.webp" \
        "image/webp"

    # List uploaded files
    print_status "Media files in S3 bucket:"
    aws --endpoint-url=$AWS_ENDPOINT_URL s3 ls "s3://$MEDIA_BUCKET/" --recursive

    print_status "Media upload completed successfully!"
}

# Main execution
main() {
    print_header "Starting GameFlex AWS data seeding..."
    echo

    if ! test_aws_connection; then
        print_error "Cannot connect to AWS/LocalStack. Make sure it's running and accessible."
        exit 1
    fi

    # Create additional Cognito users first
    create_additional_cognito_users
    echo

    # Seed database tables
    seed_users
    echo
    seed_user_profiles
    echo
    seed_channels
    echo
    seed_media
    echo
    seed_posts
    echo
    seed_comments
    echo
    seed_likes
    echo

    # Upload media files to S3
    upload_media_files
    echo

    print_status "🎉 GameFlex AWS data seeding completed successfully!"
    echo
    print_status "Summary:"
    echo "  📊 Seeded 8 users with profiles"
    echo "  🎮 Seeded 5 gaming channels"
    echo "  📝 Seeded 2 posts with media"
    echo "  💬 Seeded 10 comments"
    echo "  ❤️  Seeded 14 likes"
    echo "  📸 Uploaded 2 media files to S3"
    echo
    print_status "Your GameFlex backend is now ready for testing!"
}

# Run main function
main "$@"

# Seed Posts table
seed_posts() {
    print_header "Seeding Posts table..."

    # Clear existing posts first
    clear_posts

    local user_pool_id=$(get_user_pool_id)

    # Get Cognito user IDs for proper mapping
    local john_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")
    local jane_cognito_id=$(get_cognito_user_id "<EMAIL>" "$user_pool_id")

    # Array of posts
    local posts=(
        '20000000-0000-0000-0000-********0001:********-0000-0000-0000-********0003:'"$john_cognito_id"':Just had an epic Call of Duty session! Check out this clutch moment 🎮🔥:cod_screenshot.jpg:image:'$MEDIA_BUCKET':user/********-0000-0000-0000-********0003/cod_screenshot.jpg:12:4:2024-12-28T14:30:00Z'
        '20000000-0000-0000-0000-********0002:********-0000-0000-0000-********0004:'"$jane_cognito_id"':Finally defeated this boss in Diablo! The loot was totally worth the grind 💀⚔️:diablo_screenshot.webp:image:'$MEDIA_BUCKET':user/********-0000-0000-0000-********0004/diablo_screenshot.webp:18:6:2024-12-27T16:45:00Z'
    )

    for post_data in "${posts[@]}"; do
        IFS=':' read -r post_id author_id cognito_user_id content media_id media_type s3_bucket s3_key like_count comment_count created_at <<< "$post_data"

        put_item "$POSTS_TABLE" '{
            "id": {"S": "'$post_id'"},
            "author_id": {"S": "'$author_id'"},
            "cognito_user_id": {"S": "'$cognito_user_id'"},
            "content": {"S": "'$content'"},
            "media_id": {"S": "'$media_id'"},
            "media_type": {"S": "'$media_type'"},
            "s3_bucket": {"S": "'$s3_bucket'"},
            "s3_key": {"S": "'$s3_key'"},
            "like_count": {"N": "'$like_count'"},
            "comment_count": {"N": "'$comment_count'"},
            "is_active": {"BOOL": true},
            "created_at": {"S": "'$created_at'"},
            "updated_at": {"S": "'$created_at'"}
        }' "post $post_id"
    done

    print_status "Posts seeded with Cognito IDs:"
    echo "  - <EMAIL>: $john_cognito_id"
    echo "  - <EMAIL>: $jane_cognito_id"
}

# Seed Comments table
seed_comments() {
    print_header "Seeding Comments table..."

    # Array of comments
    local comments=(
        '40000000-0000-0000-0000-********0001:20000000-0000-0000-0000-********0001:********-0000-0000-0000-********0004:Nice clutch! What loadout were you using?:3:2024-12-28T15:15:00Z'
        '40000000-0000-0000-0000-********0002:20000000-0000-0000-0000-********0001:********-0000-0000-0000-********0005:That was insane! 🔥:1:2024-12-28T16:20:00Z'
        '40000000-0000-0000-0000-********0003:20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:Great gameplay! Keep it up 👍:2:2024-12-28T17:30:00Z'
        '40000000-0000-0000-0000-********0004:20000000-0000-0000-0000-********0001:550e8400-e29b-41d4-a716-************:Which map is this? Looks intense!:0:2024-12-28T18:45:00Z'
        '40000000-0000-0000-0000-********0005:20000000-0000-0000-0000-********0002:********-0000-0000-0000-********0003:Congrats! What difficulty level?:2:2024-12-27T17:30:00Z'
        '40000000-0000-0000-0000-********0006:20000000-0000-0000-0000-********0002:********-0000-0000-0000-********0005:The loot looks amazing! 💎:4:2024-12-27T18:15:00Z'
        '40000000-0000-0000-0000-********0007:20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:Epic boss fight! How long did it take?:1:2024-12-27T19:00:00Z'
        '40000000-0000-0000-0000-********0008:20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:I need to try this boss next! Any tips?:3:2024-12-27T20:30:00Z'
        '40000000-0000-0000-0000-********0009:20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:Diablo never gets old! 🔥⚔️:2:2024-12-27T21:15:00Z'
        '40000000-0000-0000-0000-********0010:20000000-0000-0000-0000-********0002:550e8400-e29b-41d4-a716-************:What class are you playing?:0:2024-12-27T22:00:00Z'
    )

    for comment_data in "${comments[@]}"; do
        IFS=':' read -r comment_id post_id user_id content like_count created_at <<< "$comment_data"

        # Check if comment already exists
        if item_exists "$COMMENTS_TABLE" '{"id":{"S":"'$comment_id'"}}'; then
            print_status "Comment $comment_id already exists, skipping"
            continue
        fi

        put_item "$COMMENTS_TABLE" '{
            "id": {"S": "'$comment_id'"},
            "post_id": {"S": "'$post_id'"},
            "user_id": {"S": "'$user_id'"},
            "content": {"S": "'$content'"},
            "like_count": {"N": "'$like_count'"},
            "is_active": {"BOOL": true},
            "created_at": {"S": "'$created_at'"},
            "updated_at": {"S": "'$created_at'"}
        }' "comment $comment_id"
    done
}
