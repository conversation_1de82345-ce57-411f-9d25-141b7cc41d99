#!/bin/bash

echo "Testing DynamoDB operations..."

# Test 1: Simple put-item
echo "Test 1: Simple put-item"
timeout 10 aws dynamodb put-item \
    --table-name gameflex-development-Users \
    --item '{"id":{"S":"test-simple"},"email":{"S":"<EMAIL>"}}' \
    && echo "✅ Simple put-item works" || echo "❌ Simple put-item failed"

# Test 2: Put-item with complex JSON
echo "Test 2: Complex put-item"
timeout 10 aws dynamodb put-item \
    --table-name gameflex-development-Users \
    --item '{
        "id": {"S": "test-complex"},
        "email": {"S": "<EMAIL>"},
        "username": {"S": "testuser"},
        "display_name": {"S": "Test User"},
        "bio": {"S": "This is a test user"},
        "is_active": {"BOOL": true},
        "created_at": {"S": "2024-01-01T00:00:00Z"}
    }' \
    && echo "✅ Complex put-item works" || echo "❌ Complex put-item failed"

# Test 3: Put-item with file
echo "Test 3: Put-item with file"
cat > /tmp/test-item.json << 'EOF'
{
    "id": {"S": "test-file"},
    "email": {"S": "<EMAIL>"},
    "username": {"S": "fileuser"},
    "display_name": {"S": "File User"},
    "bio": {"S": "This is a file test user"},
    "is_active": {"BOOL": true},
    "created_at": {"S": "2024-01-01T00:00:00Z"}
}
EOF

timeout 10 aws dynamodb put-item \
    --table-name gameflex-development-Users \
    --item file:///tmp/test-item.json \
    && echo "✅ File put-item works" || echo "❌ File put-item failed"

rm -f /tmp/test-item.json

echo "DynamoDB tests completed"
