# SAM starter Front End Application

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Runs unit tests
```
npm run test
```

### Connect to Backend

Make backend API endpoint accessible as an environment variable. For local, create a `.env` file, Here is an example: 
```
VUE_APP_API_ENDPOINT=http://127.0.0.1:3000/
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).
